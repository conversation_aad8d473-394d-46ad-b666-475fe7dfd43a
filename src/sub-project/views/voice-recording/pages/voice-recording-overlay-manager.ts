/**
 * 语音录制浮窗管理器
 */
import React from "react";
import { callNativeWithPromise, HippyEventEmitter } from "@hippy/react";
import abcOverlay, { OverlayViewKey } from "../../../base-ui/views/abc-overlay";
import type { VoiceRecordingOverlayViewProps, ASRStartResult } from "../utils/types";
import { TAG } from "../utils/voice-recording-utils";
import { AbcASTSocketOn, VoiceRecordSegment } from "../utils/types";
import { Permission, PermissionStatus, PermissionType } from "../../../common-base-module/permission/permission";
import { Version } from "../../../base-ui/utils/version-utils";
import { AppInfo } from "../../../base-business/config/app-info";
import { showQueryDialog } from "../../../base-ui/dialog/dialog-builder";
import { FileDownloader } from "../../../base-business/file/file-downloader";
import FileUtils from "../../../common-base-module/file/file-utils";
import { UUIDGen } from "../../../common-base-module/utils";
import { Asr, AsrResult } from "../../../AI/asr";
import { ABCApiNetwork } from "../../../net";
import { MedicalRecord, Patient } from "../../../base-business/data/beans";
import { ASR_CALLBACK, CALL_STATE, DATA_EVENTS, RECORDING_ACTION_TYPES, RECORDING_EVENTS } from "../utils/constants";
import { delayed } from "../../../common-base-module/rxjs-ext/rxjs-ext";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";

export interface MRKey {
    key: string;
    label: string;
    matchLabel: string;
}

// 全局管理类
export class VoiceRecordingOverlayManager {
    public static instance: VoiceRecordingOverlayManager;
    public overlayRef: any = null; // VoiceRecordingOverlayView 引用
    public recordingRef: any = null; // VoiceRecordingPage 引用
    public currentProps: VoiceRecordingOverlayViewProps = {};
    public currentSessionId: string | null = "init";
    public hippyEventEmitter: HippyEventEmitter | null = null;
    public callback: ((res: any) => void) | undefined;
    public medicalRecordData: any = null;
    public draft: any;

    private animationId: NodeJS.Timeout | null = null;
    private isAnimating = false;
    private pendingWaveformData: any[] = [];
    private callState = false;

    static getInstance(): VoiceRecordingOverlayManager {
        if (!VoiceRecordingOverlayManager.instance) {
            VoiceRecordingOverlayManager.instance = new VoiceRecordingOverlayManager();
        }
        return VoiceRecordingOverlayManager.instance;
    }

    // 启动overlay，需要外部传入组件类
    run(props: VoiceRecordingOverlayViewProps = {}, VoiceRecordingOverlayViewComponent?: React.ComponentType<any>): void {
        this.currentProps = { ...props };
        console.log(TAG, "VoiceRecordingOverlayManager.run - props:", this.currentProps);

        if (VoiceRecordingOverlayViewComponent) {
            const overlayView = React.createElement(VoiceRecordingOverlayViewComponent, {
                ref: (ref: any) => (this.overlayRef = ref),
                ...this.currentProps,
                onStartRecord: this.handleStartRecord,
                onStopRecord: this.handleStopRecord,
            });

            abcOverlay.show(overlayView, OverlayViewKey.voiceRecordingOverlay);
        } else {
            console.warn(TAG, "VoiceRecordingOverlayViewComponent not provided to run method");
        }
    }

    stop(): void {
        // 停止动画循环
        this.pauseAnimation();
        // 清理待处理的数据
        this.pendingWaveformData = [];

        callNativeWithPromise("AbcASR", "release");
        abcOverlay.hide(OverlayViewKey.voiceRecordingOverlay);
    }

    async show(
        enableMedicalRecordTranscription?: boolean,
        voiceRecordResult?: AsrResult,
        businessId?: string,
        patient?: Patient,
        chineseExamination?: number,
        physicalExamination?: number,
        medicalRecordType?: number,
        draft?: any,
        callback?: (res: any) => void
    ): Promise<boolean> {
        // 判断版本，低于版本给出升级提示
        const greaterThan286 = new Version(AppInfo.appVersion).compareTo(new Version("2.8.6.0000")) >= 0;
        if (!greaterThan286) {
            showQueryDialog("更新提示", "当前版本不支持语音病历，请更新后体验新功能", "立即更新", "取消", undefined, "rgba(0,0,0,0.8)");
            return false;
        }
        let voiceRecordDetail = null;
        // 请求详情接口
        if (voiceRecordResult && voiceRecordResult.id) {
            voiceRecordDetail = await Asr.getAsrResultById(voiceRecordResult.id);
            console.log("语音识别详情:", voiceRecordDetail);
            if (voiceRecordDetail?.result) {
                voiceRecordDetail?.result.forEach((item: VoiceRecordSegment, index: number) => {
                    item.id = "segment_" + index;
                });
            }
        }
        const showMedicalRecordView = Object.keys(voiceRecordDetail ?? {}).length > 0;
        this.currentProps.showRecord = true;
        if (this.overlayRef) {
            this.overlayRef.setState({
                showRecord: true,
                enableMedicalRecordTranscription: enableMedicalRecordTranscription,
                // 确保显示时是全屏状态，这样会触发淡入动画
                isMinimized: false,
                // 预先设置透明度为0，避免闪烁
                fullscreenOpacity: 0,
                minimizedOpacity: 0,
                businessId: businessId ?? "",
                chineseExamination: chineseExamination ?? 0, // 中医问诊
                physicalExamination: physicalExamination ?? 0, // 体格检查
                medicalRecordType: medicalRecordType ?? 0, // 病历类型
            });
        }
        if (showMedicalRecordView) {
            this.callback = callback;
            await this.overlayRef.navigateMedicalRecordPage(this.callback);
            this.recordingRef.setState({
                // 回显相关
                voiceRecordSegments: voiceRecordDetail?.result ?? [], // 语音识别结果
                voiceRecordResult: voiceRecordDetail, // 语音识别结果
                showMedicalRecordView: showMedicalRecordView, // 是否显示病历
                medicalRecordData: voiceRecordDetail?.aiResult ?? "未识别到有效病历内容", // 病历数据
                totalDuration: voiceRecordDetail?.duration ?? 0,
                isTranscribing: false,
                patient: patient,
            });
            VoiceRecordingOverlayManager.instance.draft = draft;
        }
        return true;
    }

    hide(): void {
        // 停止动画循环
        this.pauseAnimation();
        // 清理待处理的数据
        this.pendingWaveformData = [];

        this.currentProps.showRecord = false;
        if (this.overlayRef) {
            this.overlayRef.setState({
                showRecord: false,
                showMedicalRecordView: false,
            });
        }
    }

    // 开始录制
    handleStartRecord = async (): Promise<void> => {
        try {
            const hasPermissions = await Permission.checkPermission(PermissionType.microphone, true);
            if (hasPermissions != PermissionStatus.granted && hasPermissions != PermissionStatus.restricted) {
                this.showHighPriorityToast(
                    "需要打开麦克风权限",
                    `ABC数字医疗云需要获得您的录制音频权限，以提供语音转病历的功能服务，前往设置打开麦克风权限。`,
                    "去打开",
                    "取消",
                    () => {
                        Permission.openAppSettings();
                    },
                    () => {
                        console.log(TAG, "handleStartRecord - cancel");
                    }
                );
                return;
            }
            // const hasPhonePermission = await Permission.checkPermission(PermissionType.phone, true);
            // if (hasPhonePermission != PermissionStatus.granted && hasPhonePermission != PermissionStatus.restricted) {
            //     this.showHighPriorityToast(
            //         "需要打开通话权限",
            //         `ABC数字医疗云需要获得您的通话权限，以提供语音转病历的功能服务，前往设置打开通话权限。`,
            //         "去打开",
            //         "取消",
            //         () => {
            //             Permission.openAppSettings();
            //         },
            //         () => {
            //             console.log(TAG, "handleStartRecord - cancel");
            //         }
            //     );
            //     return;
            // }
            if (this.overlayRef.state.enableMedicalRecordTranscription) {
                await callNativeWithPromise("AbcASR", "emitMessage", {
                    event: RECORDING_EVENTS.RECORDING_ACTION,
                    data: {
                        event: RECORDING_ACTION_TYPES.START_RECORDING,
                        businessId: this.overlayRef.state.businessId,
                        businessType: 0,
                    },
                });
            }
            await callNativeWithPromise("AbcASR", "emitMessage", {
                event: RECORDING_EVENTS.RECORDING_ACTION,
                data: {
                    event: RECORDING_ACTION_TYPES.RECORDING_STARTED,
                },
            });
            await callNativeWithPromise("AbcASR", "startService", {});
            const result: ASRStartResult = await callNativeWithPromise("AbcASR", "startRecognize", { audioDataName: "audio-data" });
            this.currentSessionId = result.sessionId;

            // 播放开始录制音效
            if (this.overlayRef) {
                this.overlayRef.playStartSound();
            }

            this.updateRecordingState(true);

            this.updateWaveformData([]);

            this.overlayRef?.setState({
                recognitionResults: [],
            });

            // 开始计时
            if (this.overlayRef) {
                this.overlayRef.resetDurationTimer();
                this.overlayRef.startDurationTimer();
            }
            this.currentProps.onStartRecord?.();
        } catch (e) {
            console.error(TAG, "handleStartRecord error:", e);
        }
    };

    // 停止录制
    handleStopRecord = async (): Promise<void> => {
        try {
            await callNativeWithPromise("AbcASR", "stopService", {});
            await callNativeWithPromise("AbcASR", "stopRecognize", { sessionId: this.currentSessionId });
            // 停止动画循环
            this.pauseAnimation();
            // 清理待处理的数据
            this.pendingWaveformData = [];

            this.updateRecordingState(false);
            this.updateWaveformData([]);
            this.currentSessionId = null; // 清空会话ID

            if (this.overlayRef) {
                this.overlayRef.stopDurationTimer();
                this.overlayRef.resetDurationTimer();
            }
            await delayed(500).toPromise();
            if (this.overlayRef.state.enableMedicalRecordTranscription) {
                await callNativeWithPromise("AbcASR", "emitMessage", {
                    event: RECORDING_EVENTS.RECORDING_ACTION,
                    data: {
                        event: RECORDING_ACTION_TYPES.STOP_RECORDING,
                    },
                });
            } else {
                await callNativeWithPromise("AbcASR", "emitMessage", {
                    event: RECORDING_EVENTS.RECORDING_ACTION,
                    data: {
                        event: RECORDING_ACTION_TYPES.RECORDING_STOPPED,
                    },
                });
                this.overlayRef.navigateDialog({
                    title: "",
                    content: "录音完成，请在电脑端查看病历",
                    icon: "s-singlecheck-line",
                });
                setTimeout(() => {
                    ABCNavigator.pop();
                }, 2000);
            }
        } catch (e) {
            console.error(TAG, "handleStopRecord error:", e);
        }
    };

    private async asrParseSSE(resultText: string, chineseExamination: string, physicalExamination: string, medicalRecordType: string) {
        let medicalRecordDataContent = "";
        const res = await ABCApiNetwork.connectSSE("ai/analysis/text", {
            method: "POST",
            body: {
                text: resultText,
                data: {
                    chineseExamination: {
                        value: chineseExamination,
                    },
                    physicalExamination: {
                        value: physicalExamination,
                    },
                    medicalRecordType: {
                        value: medicalRecordType,
                    },
                },
                promptName: "asr-generate-medical-record",
            },
            listeners: {
                onopen: (event) => {
                    console.log("连接已打开:", event.connectionId);
                },
                onmessage: (event) => {
                    const data = JSON.parse(event.data);
                    if (!data || data.code !== 200) {
                        console.error("接口错误:", data);
                        return;
                    }

                    // 处理回答内容
                    if (data.data.answer) {
                        medicalRecordDataContent += data.data.answer;
                        this.recordingRef?.setState({
                            medicalRecordData: medicalRecordDataContent,
                        });
                    }
                    console.log("收到消息:", data);
                },
                onerror: (event) => {
                    console.error("连接错误:", event.error);
                },
            },
        });
        return { medicalRecordDataContent, res };
    }

    updateRecordingState(isRecording: boolean): void {
        this.currentProps.isRecording = isRecording;
        if (this.overlayRef) {
            this.overlayRef.setState({ isRecording });
        }
    }

    updateWaveformData(waveformData: number[]): void {
        this.currentProps.waveformData = waveformData;
        if (this.recordingRef) {
            this.recordingRef.setState({
                waveformData: waveformData,
            });
        }
        if (this.overlayRef) {
            this.overlayRef.setState({
                waveformData: waveformData,
            });
        }
    }

    setOverlayRef(ref: any): void {
        this.overlayRef = ref;
    }

    setRecordingRef(ref: any): void {
        this.recordingRef = ref;
    }

    public showHighPriorityToast(
        titleOrMessage: string,
        content?: string,
        confirmText?: string,
        cancelText?: string,
        onConfirm?: () => void,
        onCancel?: () => void
    ): void {
        if (this.overlayRef) {
            // 如果只有一个参数，显示简单Toast
            if (!content) {
                this.overlayRef.setState({ toastMessage: titleOrMessage, toastVisible: true });
                setTimeout(() => {
                    this.overlayRef?.setState({ toastVisible: false, toastMessage: null });
                }, 3000);
            } else {
                // 如果有多个参数，显示权限弹窗
                this.overlayRef.setState({
                    dialogVisible: true,
                    dialogTitle: titleOrMessage,
                    dialogContent: content,
                    dialogConfirmText: confirmText || "确认",
                    dialogCancelText: cancelText || "取消",
                    dialogOnConfirm: onConfirm || null,
                    dialogOnCancel: onCancel || null,
                });
            }
        }
    }

    public registerSocketEventListeners(): void {
        callNativeWithPromise("AbcASR", "eventOn", { event: RECORDING_EVENTS.RECORDING_ACTION });
        callNativeWithPromise("AbcASR", "eventOn", { event: DATA_EVENTS.ASR_RESULT });
        callNativeWithPromise("AbcASR", "eventOn", { event: DATA_EVENTS.ASR_COMPLETED });
        callNativeWithPromise("AbcASR", "eventOn", { event: DATA_EVENTS.WAVEFORM_DATA });
        callNativeWithPromise("AbcASR", "listenerCall", {});
    }

    public registerASREventListeners(): void {
        this.hippyEventEmitter = new HippyEventEmitter();
        this.hippyEventEmitter.addListener("AbcASRSocketOn", this.handleSocketResult.bind(this));
        this.hippyEventEmitter.addListener("AbcASRCallback", this.handleAsrCallback.bind(this));
    }

    public handleSocketResult(evt: AbcASTSocketOn): void {
        console.log(TAG, "handleSocketResult:", evt);
        const { event, message } = evt;
        if (event === DATA_EVENTS.ASR_RESULT) {
            const messageObj = JSON.parse(message);
            this.handleASRResult(messageObj);
        } else if (event === DATA_EVENTS.WAVEFORM_DATA) {
            const messageObj = JSON.parse(message);
            console.log(TAG, "handleSocketResult - waveform-data:", messageObj);
        } else if (event === RECORDING_EVENTS.RECORDING_ACTION) {
            const messageObj = JSON.parse(message);
            this.handleRecordingAction(messageObj);
        } else if (event === DATA_EVENTS.ASR_COMPLETED) {
            console.log(TAG, "handleSocketResult - asr-completed");
        }
    }

    private animate = (): void => {
        this.processPendingWaveformData();
        if (this.isAnimating) {
            this.animationId = setTimeout(this.animate, 17); // 约30fps
        }
    };

    private startAnimation = (): void => {
        if (!this.isAnimating) {
            this.isAnimating = true;
            this.animate();
        }
    };

    private pauseAnimation = (): void => {
        if (this.animationId) {
            clearTimeout(this.animationId);
            this.animationId = null;
        }
        this.isAnimating = false;
    };

    private processPendingWaveformData = (): void => {
        if (this.pendingWaveformData.length === 0 || !this.overlayRef) {
            const waveformData = [...this.overlayRef.state.waveformData];
            if (waveformData.length > 150) {
                waveformData.shift();
            }
            VoiceRecordingOverlayManager.instance.updateWaveformData(waveformData);
            return;
        }
        const dataToProcess = [...this.pendingWaveformData];
        this.pendingWaveformData = [];
        const waveformData = [...this.overlayRef.state.waveformData];

        dataToProcess.forEach((data) => {
            waveformData.push(data);
            if (waveformData.length > 150) {
                waveformData.shift();
            }
        });

        VoiceRecordingOverlayManager.instance.updateWaveformData(waveformData);
        if (dataToProcess.length > 0) {
            callNativeWithPromise("AbcASR", "emitMessage", {
                event: DATA_EVENTS.WAVEFORM_DATA,
                data: {
                    waveform_data: dataToProcess,
                },
            });
        }
    };

    public handleASRCompleted(): void {
        // 显示病历转写视图并开始转写
        this.overlayRef.setState({
            showMedicalRecordView: true,
            isMinimized: false,
        });
        this.overlayRef.navigateMedicalRecordPage(this.callback).then((res: any) => {
            this.callback?.(res);
        });
        let medicalRecordDataContent = "";
        let resultText = "";
        for (let i = 0; i < this.overlayRef.state.recognitionResults.length; i++) {
            resultText += this.overlayRef.state.recognitionResults[i].text;
        }
        Asr.getAsrResultByTaskId(this.overlayRef.state.taskId, this.overlayRef.state.businessId).then((voiceRecordDetail) => {
            this.asrParseSSE(
                resultText,
                this.overlayRef.state.chineseExamination,
                this.overlayRef.state.physicalExamination,
                this.overlayRef.state.medicalRecordType
            ).then((__ret: any) => {
                medicalRecordDataContent = __ret.medicalRecordDataContent;
                const res = __ret.res;
                console.log(
                    "初始文本-> ",
                    resultText,
                    "全部结束->",
                    medicalRecordDataContent,
                    "res ->",
                    res,
                    "messageObj->" + JSON.stringify(voiceRecordDetail)
                );
                if (voiceRecordDetail) {
                    Asr.saveAsrResult(voiceRecordDetail.id, medicalRecordDataContent);
                }
                if (voiceRecordDetail && voiceRecordDetail.result) {
                    voiceRecordDetail.result.forEach((item: VoiceRecordSegment, index: number) => {
                        item.id = "segment_" + index;
                    });
                }
                this.recordingRef.setState({
                    isTranscribing: false,
                    voiceRecordResult: voiceRecordDetail,
                    voiceRecordSegments: voiceRecordDetail?.result || [],
                    totalDuration: voiceRecordDetail?.duration || 0,
                });
            });
        });
        return;
    }

    public handleAsrCallback(evt: any): void {
        console.log(TAG, "handleAsrCallback:", evt);
        const { event, payload } = evt;
        if (event === ASR_CALLBACK.ON_WAVEFORM_DATA) {
            // 将数据添加到待处理队列，而不是立即处理
            this.pendingWaveformData.push(payload.data);

            // 启动动画循环（如果尚未启动）
            this.startAnimation();
        }
        if (event === ASR_CALLBACK.ON_CALL_STATE_CHANGED) {
            if (payload.state === CALL_STATE.OFFHOOK) {
                this.callState = true;
                callNativeWithPromise("AbcASR", "emitMessage", {
                    event: RECORDING_EVENTS.RECORDING_ACTION,
                    data: {
                        event: RECORDING_ACTION_TYPES.RECORDING_INTERRUPTED,
                    },
                });
                this.overlayRef.handleStartPauseRecord();
            }
            if (payload.state === CALL_STATE.IDLE && this.callState) {
                this.callState = false;
                callNativeWithPromise("AbcASR", "emitMessage", {
                    event: RECORDING_EVENTS.RECORDING_ACTION,
                    data: {
                        event: RECORDING_ACTION_TYPES.RESUME_RECORDING,
                    },
                });
                this.overlayRef.handleStartPauseRecord();
            }
        }
    }

    public handleASRResult(result: any): void {
        const { code, message } = result;
        if (code != 0 || message != "success") {
            return;
        }

        if (!result.result) {
            return;
        }
        const { sliceType, voiceText } = result.result;

        console.log(TAG, "handleASRResult:", result);

        if (this.overlayRef && this.overlayRef.setState) {
            this.overlayRef.setState(
                (prevState: { recognitionResults: any }) => ({
                    recognitionResults: [
                        ...prevState.recognitionResults.filter((item: any) => item.isFinal),
                        {
                            text: voiceText || "",
                            isFinal: sliceType == 2,
                        },
                    ],
                }),
                () => {
                    console.log("临时结果更新:", this.overlayRef.state.recognitionResults);
                }
            );
        }
    }

    private handleRecordingAction(args: any): void {
        const { event, data } = args;
        console.log("收到控制指令:", event, data);
        if (!event) {
            return;
        }
        if (event === RECORDING_ACTION_TYPES.START_RECORDING) {
            const isShow = this.show(false);
            if (!isShow) return;
            if (this.overlayRef.state.isMinimized) {
                this.overlayRef.handleToggleSize();
            }
            this.handleStartRecord();
        } else if (event === RECORDING_ACTION_TYPES.RECORDING_STARTED) {
            const { taskId, businessId } = data;
            this.handleStarted(taskId, businessId);
        } else if (event === RECORDING_ACTION_TYPES.STOP_RECORDING) {
            this.overlayRef.handleStopRecord();
        } else if (event === RECORDING_ACTION_TYPES.RECORDING_STOPPED) {
            if (this.overlayRef.state.enableMedicalRecordTranscription && this.overlayRef.state.showRecord) {
                this.handleASRCompleted();
            }
        } else if (event === RECORDING_ACTION_TYPES.PAUSE_RECORDING) {
            this.overlayRef.handleStartPauseRecord();
        } else if (event === RECORDING_ACTION_TYPES.RESUME_RECORDING) {
            this.overlayRef.handleStartPauseRecord();
        }
    }

    private handleStarted(taskId: string, businessId: string): void {
        console.log(TAG, "handleJoin:", taskId, businessId);
        this.overlayRef.setState({
            taskId,
            businessId,
        });
    }

    public parseMedicalRecord = (medicalRecord: string | null, mrKeys: MRKey[]): any => {
        const result: MedicalRecord = new MedicalRecord();
        if (!medicalRecord) {
            return result;
        }
        const trimedMedicalRecord = medicalRecord.split("**").join("");
        mrKeys.forEach((item) => {
            const { key, matchLabel } = item;
            const regex = new RegExp(`${matchLabel}[：:](.*?)(?=\\n\\n|\\n[^\\n]|$)`, "s");
            const match = trimedMedicalRecord?.match(regex);

            if (match && match[1]?.trim()) {
                (result as any)[key] = match[1].trim();
            }
        });
        console.log("解析后的病历数据:", { ...result });
        this.medicalRecordData = result;
        return result;
    };

    /**
     * 语音合成控制逻辑方法
     * 下载多段音频文件，读取二进制数据，合成为一段音频，然后写回终端
     * @param voiceUrls 音频URL数组
     * @returns Promise<string> 合成后的音频文件路径
     */
    public async synthesizeVoiceRecording(voiceUrls: string[]): Promise<string> {
        console.log(TAG, "synthesizeVoiceRecording start, voiceUrls:", voiceUrls);

        if (!voiceUrls || voiceUrls.length === 0) {
            throw new Error("语音URL数组不能为空");
        }

        try {
            // 1. 下载所有音频文件
            const downloadedFiles = await this.downloadVoiceFiles(voiceUrls);
            console.log(TAG, "downloadedFiles:", downloadedFiles);

            // 2. 读取所有音频文件的二进制数据
            const audioDataList = await this.readVoiceFiles(downloadedFiles);
            console.log(TAG, "audioDataList length:", audioDataList.length);

            // 3. 合成音频数据
            const synthesizedAudioData = await this.mergeAudioData(audioDataList);
            console.log(TAG, "synthesizedAudioData length:", synthesizedAudioData.length);

            // 4. 写入合成后的音频文件
            const outputFilePath = await this.writeSynthesizedAudio(synthesizedAudioData);
            console.log(TAG, "synthesizeVoiceRecording completed, outputFilePath:", outputFilePath);

            // 5. 清理临时文件
            await this.cleanupTempFiles(downloadedFiles);

            return outputFilePath;
        } catch (error) {
            console.error(TAG, "synthesizeVoiceRecording error:", error);
            throw error;
        }
    }

    /**
     * 下载语音文件
     * @param voiceUrls 音频URL数组
     * @returns Promise<string[]> 下载后的文件路径数组
     */
    private async downloadVoiceFiles(voiceUrls: string[]): Promise<string[]> {
        const downloadedFiles: string[] = [];
        const tmpDir = await FileUtils.getTmpDir();

        for (let i = 0; i < voiceUrls.length; i++) {
            const url = voiceUrls[i];
            const fileName = `voice_segment_${i}_${UUIDGen.generate()}.wav`;
            const filePath = `${tmpDir}/${fileName}`;

            try {
                console.log(TAG, `Downloading voice file ${i + 1}/${voiceUrls.length}: ${url}`);

                // 使用FileDownloader.downloadFile方法下载文件
                const response = await FileDownloader.downloadFile(url, {
                    filePath: filePath,
                    method: "GET",
                    redirect: "follow",
                    onProgress: (currentBytes, totalBytes) => {
                        const progress = Math.round((currentBytes / totalBytes) * 100);
                        console.log(TAG, `Download progress for file ${i + 1}: ${progress}%`);
                    },
                });

                console.log(TAG, `Download response:`, response);
                if (response && response.filePath) {
                    downloadedFiles.push(response.filePath);
                } else {
                    throw new Error(`下载文件失败: ${url}`);
                }
            } catch (error) {
                console.error(TAG, `下载文件失败 ${url}:`, error);
                throw error;
            }
        }

        return downloadedFiles;
    }

    /**
     * 读取语音文件的二进制数据
     * @param filePaths 文件路径数组
     * @returns Promise<string[]> 音频数据数组（base64编码）
     */
    private async readVoiceFiles(filePaths: string[]): Promise<string[]> {
        const audioDataList: string[] = [];

        for (const filePath of filePaths) {
            try {
                console.log(TAG, `Reading voice file: ${filePath}`);

                const audioData: string = await FileUtils.readAsString(filePath, "base64");

                if (audioData) {
                    audioDataList.push(audioData);
                } else {
                    throw new Error(`读取文件失败: ${filePath}`);
                }
            } catch (error) {
                console.error(TAG, `读取文件失败 ${filePath}:`, error);
                throw error;
            }
        }

        return audioDataList;
    }

    /**
     * 合成音频数据
     * @param audioDataList 音频数据数组（base64编码）
     * @returns Promise<string> 合成后的音频数据（base64编码）
     */
    private async mergeAudioData(audioDataList: string[]): Promise<string> {
        console.log(TAG, "Merging audio data...");
        try {
            // 将base64数据转换为字节数组
            const audioByteArrays: Uint8Array[] = [];
            for (const base64Data of audioDataList) {
                const bytes = this.base64ToUint8Array(base64Data);
                audioByteArrays.push(bytes);
            }

            // 调用WAV文件合成方法
            const mergedBytes = this.concatenateWavFiles(audioByteArrays);
            if (!mergedBytes) {
                throw new Error("WAV文件合成失败");
            }

            // 合成后的音频计算波形数据，调用this.updateWaveformData()保存
            const waveformData = this.analyzeWaveform(mergedBytes);
            this.updateWaveformData(waveformData);

            // 将合成后的字节数组转换回base64
            const mergedBase64 = this.uint8ArrayToBase64(mergedBytes);

            console.log(TAG, "Audio data merged successfully");
            return mergedBase64;
        } catch (error) {
            console.error(TAG, "合成音频数据失败:", error);
            throw error;
        }
    }

    /**
     * 合成WAV文件
     * 参考Java版本的concatenateWavFiles方法实现
     * @param audioDataList 音频字节数组列表
     * @returns Uint8Array | null 合成后的音频数据
     */
    private concatenateWavFiles(audioDataList: Uint8Array[]): Uint8Array | null {
        if (!audioDataList || audioDataList.length === 0) {
            return null;
        }

        try {
            // WAV文件结构：
            // 前44字节是WAV头，包含文件大小、格式等信息
            // 44字节之后是实际音频数据

            // 获取第一个文件的头信息
            const firstFile = audioDataList[0];
            if (firstFile.length < 44) {
                return null; // 文件太小，不是有效的WAV
            }

            // 计算所有文件的音频数据总长度
            let totalAudioDataSize = 0;
            for (let i = 0; i < audioDataList.length; i++) {
                const file = audioDataList[i];
                if (file.length < 44) continue; // 跳过无效文件

                // 从每个WAV文件中提取数据大小（不包括头）
                totalAudioDataSize += file.length - 44;
            }

            // 创建新的WAV文件（头 + 所有数据）
            const result = new Uint8Array(44 + totalAudioDataSize);

            // 复制第一个文件的头信息
            result.set(firstFile.slice(0, 44), 0);

            // 更新WAV头中的文件大小信息
            // 总文件大小 = 总数据大小 + 36 (RIFF块中的大小)
            const totalSize = totalAudioDataSize + 36;
            result[4] = totalSize & 0xff;
            result[5] = (totalSize >> 8) & 0xff;
            result[6] = (totalSize >> 16) & 0xff;
            result[7] = (totalSize >> 24) & 0xff;

            // 更新数据块大小
            result[40] = totalAudioDataSize & 0xff;
            result[41] = (totalAudioDataSize >> 8) & 0xff;
            result[42] = (totalAudioDataSize >> 16) & 0xff;
            result[43] = (totalAudioDataSize >> 24) & 0xff;

            // 复制所有文件的音频数据（跳过每个文件的头）
            let offset = 44; // 从头之后开始
            for (const file of audioDataList) {
                if (file.length < 44) continue; // 跳过无效文件

                // 复制当前文件的音频数据（跳过头）
                result.set(file.slice(44), offset);
                offset += file.length - 44;
            }

            return result;
        } catch (error) {
            console.error(TAG, "concatenateWavFiles error:", error);
            return null;
        }
    }

    /**
     * 写入合成后的音频文件
     * @param audioData 合成后的音频数据（base64编码）
     * @returns Promise<string> 输出文件路径
     */
    private async writeSynthesizedAudio(audioData: string): Promise<string> {
        const tmpDir = await FileUtils.getTmpDir();
        const outputFileName = `synthesized_voice_${UUIDGen.generate()}.wav`;
        const outputFilePath = `${tmpDir}/${outputFileName}`;

        try {
            console.log(TAG, `Writing synthesized audio to: ${outputFilePath}`);

            const success = await callNativeWithPromise("FileManager", "writeAsString", outputFilePath, audioData, "base64");

            if (success) {
                console.log(TAG, "Synthesized audio written successfully");
                return outputFilePath;
            } else {
                throw new Error("写入合成音频文件失败");
            }
        } catch (error) {
            console.error(TAG, "写入合成音频文件失败:", error);
            throw error;
        }
    }

    /**
     * 清理临时文件
     * @param filePaths 要清理的文件路径数组
     */
    private async cleanupTempFiles(filePaths: string[]): Promise<void> {
        console.log(TAG, "Cleaning up temporary files...");

        for (const filePath of filePaths) {
            try {
                await FileUtils.deleteFile(filePath);
                console.log(TAG, `Deleted temp file: ${filePath}`);
            } catch (error) {
                console.warn(TAG, `Failed to delete temp file ${filePath}:`, error);
            }
        }
    }

    /**
     * 分析音频波形数据
     * 参考Java版本的analyzeWaveform方法实现
     * @param audioData 音频字节数组（包含WAV头）
     * @returns number[] 波形数据数组
     */
    private analyzeWaveform(audioData: Uint8Array): number[] {
        // 跳过WAV文件头（前44字节），只分析音频数据部分
        const audioDataOnly = audioData.slice(44);

        // 计算总样本数（每2字节一个样本）
        const totalSamples = Math.floor(audioDataOnly.length / 2);

        // 使用浮点数计算采样间隔
        const samplesPerInterval = totalSamples / 120.0;

        const rawVolumes: number[] = new Array(110); // 存储原始音量值
        const smoothedVolumes: number[] = new Array(110); // 存储平滑后的音量值

        // 调整平滑因子α为0.5，增强音量特征保留能力
        const alpha = 0.5;

        for (let j = 0; j < 110; j++) {
            let maxVolume = 0; // 使用最大值代替平均值
            let sampleCount = 0;

            // 计算当前区间的开始和结束位置
            const start = Math.floor(j * samplesPerInterval);
            const end = Math.min(Math.floor((j + 1) * samplesPerInterval), totalSamples);

            for (let i = start; i < end; i++) {
                const byteIndex = i * 2;
                if (byteIndex + 1 < audioDataOnly.length) {
                    // 读取16位有符号音频样本（小端序）
                    const sample = (audioDataOnly[byteIndex + 1] << 8) | audioDataOnly[byteIndex];
                    // 转换为有符号16位整数
                    const signedSample = sample > 32767 ? sample - 65536 : sample;
                    // 计算绝对值并归一化到0-1范围
                    const volume = Math.abs(signedSample / 32767.0);
                    // 记录区间内最大音量值
                    if (volume > maxVolume) {
                        maxVolume = volume;
                    }
                    sampleCount++;
                }
            }

            // 使用区间最大音量值
            rawVolumes[j] = sampleCount > 0 ? maxVolume : 0;

            // 应用指数移动平均(EMA)算法
            if (j === 0) {
                smoothedVolumes[j] = rawVolumes[j];
            } else {
                // 增强当前采样点权重，保留更多原始特征
                smoothedVolumes[j] = alpha * rawVolumes[j] + (1 - alpha) * smoothedVolumes[j - 1];
            }
        }

        // 生成最终的波形数据数组
        const waveformData: number[] = [];
        for (let j = 0; j < 110; j++) {
            // 添加音量放大系数（1.5倍），增强特征表现
            const amplifiedVolume = smoothedVolumes[j] * 1.5;
            // 确保不超过1.0
            waveformData.push(Math.min(amplifiedVolume, 1.0));
        }

        console.log(TAG, `Waveform analysis completed, generated ${waveformData.length} data points`);
        return waveformData;
    }

    /**
     * 将base64字符串转换为Uint8Array
     * 替代浏览器的atob方法，适用于React Native环境
     * @param base64 base64编码的字符串
     * @returns Uint8Array 字节数组
     */
    private base64ToUint8Array(base64: string): Uint8Array {
        // 移除可能的数据URL前缀
        const cleanBase64 = base64.replace(/^data:.*,/, "");

        // Base64字符集
        const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        let result = "";

        // 解码base64
        for (let i = 0; i < cleanBase64.length; i += 4) {
            const a = chars.indexOf(cleanBase64[i]);
            const b = chars.indexOf(cleanBase64[i + 1]);
            const c = chars.indexOf(cleanBase64[i + 2]);
            const d = chars.indexOf(cleanBase64[i + 3]);

            const bitmap = (a << 18) | (b << 12) | (c << 6) | d;

            result += String.fromCharCode((bitmap >> 16) & 255);
            if (c !== 64) result += String.fromCharCode((bitmap >> 8) & 255);
            if (d !== 64) result += String.fromCharCode(bitmap & 255);
        }

        // 转换为Uint8Array
        const bytes = new Uint8Array(result.length);
        for (let i = 0; i < result.length; i++) {
            bytes[i] = result.charCodeAt(i);
        }

        return bytes;
    }

    /**
     * 将Uint8Array转换为base64字符串
     * 替代浏览器的btoa方法，适用于React Native环境
     * @param bytes 字节数组
     * @returns string base64编码的字符串
     */
    private uint8ArrayToBase64(bytes: Uint8Array): string {
        const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        let result = "";

        for (let i = 0; i < bytes.length; i += 3) {
            const a = bytes[i];
            const b = i + 1 < bytes.length ? bytes[i + 1] : 0;
            const c = i + 2 < bytes.length ? bytes[i + 2] : 0;

            const bitmap = (a << 16) | (b << 8) | c;

            result += chars.charAt((bitmap >> 18) & 63);
            result += chars.charAt((bitmap >> 12) & 63);
            result += i + 1 < bytes.length ? chars.charAt((bitmap >> 6) & 63) : "=";
            result += i + 2 < bytes.length ? chars.charAt(bitmap & 63) : "=";
        }

        return result;
    }
}

// 导出单例实例
export const voiceRecordingOverlayManager = VoiceRecordingOverlayManager.getInstance();
